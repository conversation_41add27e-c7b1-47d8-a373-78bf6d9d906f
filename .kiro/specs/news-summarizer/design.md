# 设计文档

## 概述

内容汇总应用是一个基于Python的本地桌面应用，采用模块化架构设计。应用通过文件系统监控、AI内容处理和结构化存储三个核心模块，实现对Obsidian/Cubox同步内容的自动汇总处理。系统设计遵循单一职责原则，确保各模块独立可测试，同时通过配置驱动的方式提供灵活性。

## 架构

### 整体架构图

```mermaid
graph TB
    A[主程序 main.py] --> B[配置管理 config.py]
    A --> C[文件监控模块 file_monitor.py]
    A --> D[汇总处理模块 summarizer.py]
    A --> E[存储模块 storage.py]
    
    C --> F[文件系统监控]
    C --> G[文件变化检测]
    
    D --> H[Gemini客户端 gemini_client.py]
    D --> I[内容解析器]
    D --> J[汇总生成器]
    
    E --> K[JSON存储]
    E --> L[Markdown输出]
    
    H --> M[Google Gemini API]
    
    subgraph "数据层"
        N[processed.json]
        O[summaries/]
        P[logs/]
    end
    
    E --> N
    E --> O
    A --> P
```

### 技术栈选择

- **核心语言**: Python 3.9+
- **AI服务**: Google Gemini 2.5 Flash API
- **文件监控**: watchdog库 - 提供跨平台文件系统事件监控
- **任务调度**: schedule库 - 轻量级定时任务管理
- **配置管理**: 内置configparser + 环境变量
- **日志系统**: 内置logging模块
- **文件处理**: pathlib + markdown库

## 组件和接口

### 1. 配置管理模块 (config.py)

```python
class Config:
    def __init__(self):
        self.api_key: str
        self.watch_directory: str
        self.scan_interval: int
        self.output_directory: str
        self.log_level: str
        self.max_concurrent_files: int
        self.retry_attempts: int
        self.chunk_size: int
    
    def load_from_env(self) -> None
    def load_from_file(self, config_path: str) -> None
    def validate(self) -> bool
```

**设计决策**: 采用环境变量优先，配置文件补充的策略，确保敏感信息（API密钥）的安全性。

### 2. 文件监控模块 (file_monitor.py)

```python
class FileMonitor:
    def __init__(self, config: Config, callback: Callable)
    
    def start_monitoring(self) -> None
    def stop_monitoring(self) -> None
    def scan_directory(self) -> List[Path]
    def is_valid_file(self, file_path: Path) -> bool
    
class FileChangeHandler(FileSystemEventHandler):
    def on_created(self, event) -> None
    def on_modified(self, event) -> None
```

**设计决策**: 结合实时监控和定时扫描，确保不遗漏任何文件变化。使用观察者模式处理文件事件。

### 3. Gemini客户端模块 (gemini_client.py)

```python
class GeminiClient:
    def __init__(self, api_key: str, retry_attempts: int = 3)
    
    def generate_summary(self, content: str, content_type: str) -> Dict[str, Any]
    def _build_prompt(self, content: str, content_type: str) -> str
    def _handle_rate_limit(self, retry_count: int) -> None
    def _chunk_content(self, content: str, max_length: int) -> List[str]
    
class ContentTypeDetector:
    @staticmethod
    def detect_type(content: str, filename: str) -> str
```

**设计决策**: 实现智能内容类型检测，根据不同类型（新闻、技术文章、普通文章）使用不同的提示词模板。

### 4. 汇总处理模块 (summarizer.py)

```python
class ContentSummarizer:
    def __init__(self, gemini_client: GeminiClient, storage: Storage)
    
    def process_file(self, file_path: Path) -> SummaryResult
    def process_batch(self, file_paths: List[Path]) -> List[SummaryResult]
    def _extract_content(self, file_path: Path) -> str
    def _validate_markdown(self, content: str) -> bool
    
@dataclass
class SummaryResult:
    file_path: str
    title: str
    core_facts: str
    key_points: List[str]
    importance_level: str
    keywords: List[str]
    summary_time: datetime
    content_type: str
    processing_status: str
```

**设计决策**: 使用数据类定义标准化的汇总结果格式，便于序列化和后续处理。

### 5. 存储模块 (storage.py)

```python
class Storage:
    def __init__(self, config: Config)
    
    def save_summary(self, summary: SummaryResult) -> bool
    def load_processed_files(self) -> Dict[str, datetime]
    def update_processed_files(self, file_path: str, timestamp: datetime) -> None
    def generate_report(self, format: str = "markdown") -> str
    def get_statistics(self) -> Dict[str, Any]
    
class ProcessedFileTracker:
    def __init__(self, tracker_file: Path)
    
    def is_processed(self, file_path: Path) -> bool
    def mark_processed(self, file_path: Path) -> None
    def get_file_hash(self, file_path: Path) -> str
```

**设计决策**: 使用文件哈希值跟踪文件变化，避免仅依赖修改时间可能出现的问题。

## 数据模型

### 配置数据模型

```python
@dataclass
class AppConfig:
    # API配置
    gemini_api_key: str
    
    # 文件监控配置
    watch_directory: str = "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox"
    scan_interval_minutes: int = 30
    file_extensions: List[str] = field(default_factory=lambda: [".md"])
    
    # 处理配置
    max_concurrent_files: int = 5
    chunk_size_chars: int = 4000
    retry_attempts: int = 3
    retry_delay_seconds: int = 5
    
    # 输出配置
    output_directory: str = "data/summaries"
    log_directory: str = "logs"
    log_level: str = "INFO"
```

### 内容类型枚举

```python
class ContentType(Enum):
    NEWS = "news"
    TECH_ARTICLE = "tech_article"
    GENERAL_ARTICLE = "general_article"
    UNKNOWN = "unknown"
```

### 提示词模板

```python
PROMPT_TEMPLATES = {
    ContentType.NEWS: """
作为专业新闻汇总助手，请对以下新闻内容进行结构化汇总：

汇总要求：
1. 提取核心事实（时间、地点、人物、事件、原因、影响）
2. 突出3-5个关键信息点
3. 评估新闻重要性等级
4. 生成3-5个关键词标签
5. 控制汇总长度在150-300字

内容：{content}
""",
    
    ContentType.TECH_ARTICLE: """
作为技术文章汇总助手，请对以下技术内容进行结构化汇总：

汇总要求：
1. 提取核心技术概念和主题
2. 总结主要方法或解决方案
3. 突出关键结论或发现
4. 评估技术重要性和实用性
5. 生成相关技术标签

内容：{content}
""",
    
    ContentType.GENERAL_ARTICLE: """
作为通用文章汇总助手，请对以下内容进行结构化汇总：

汇总要求：
1. 提取文章主要观点
2. 总结核心论据或要点
3. 评估内容价值和重要性
4. 生成相关主题标签
5. 保持客观中立的语调

内容：{content}
"""
}
```

## 错误处理

### 错误分类和处理策略

1. **API相关错误**
   - 网络连接失败：实现指数退避重试
   - API配额超限：记录错误并延迟处理
   - 认证失败：立即停止并报告错误

2. **文件系统错误**
   - 文件访问权限：跳过文件并记录警告
   - 文件不存在：从处理队列中移除
   - 磁盘空间不足：暂停处理并报告错误

3. **内容处理错误**
   - Markdown格式错误：尝试纯文本处理
   - 内容为空：跳过文件
   - 编码错误：尝试多种编码方式

### 错误恢复机制

```python
class ErrorHandler:
    def __init__(self, max_retries: int = 3):
        self.max_retries = max_retries
        self.retry_queue: Queue = Queue()
    
    def handle_api_error(self, error: Exception, context: Dict) -> bool
    def handle_file_error(self, error: Exception, file_path: Path) -> bool
    def add_to_retry_queue(self, task: ProcessingTask) -> None
    def process_retry_queue(self) -> None
```

## 测试策略

### 单元测试

1. **配置管理测试**
   - 环境变量加载
   - 配置文件解析
   - 配置验证逻辑

2. **文件监控测试**
   - 文件变化检测
   - 文件过滤逻辑
   - 监控启停功能

3. **API客户端测试**
   - Mock API响应
   - 错误处理逻辑
   - 重试机制

4. **汇总处理测试**
   - 内容解析
   - 类型检测
   - 结果格式化

### 集成测试

1. **端到端流程测试**
   - 文件添加到汇总完成的完整流程
   - 批量处理功能
   - 错误恢复流程

2. **API集成测试**
   - 真实API调用（使用测试配额）
   - 不同内容类型的处理
   - 长文档分块处理

### 性能测试

1. **负载测试**
   - 大量文件同时处理
   - 内存使用监控
   - 处理速度基准

2. **压力测试**
   - API调用频率限制
   - 文件系统I/O压力
   - 长时间运行稳定性

## 部署和运维

### 项目结构

```
news_summarizer/
├── main.py                 # 应用入口点
├── config.py              # 配置管理
├── requirements.txt       # 依赖声明
├── setup.py              # 安装脚本
├── README.md             # 使用文档
├── .env.example          # 环境变量模板
├── modules/
│   ├── __init__.py
│   ├── file_monitor.py   # 文件监控
│   ├── gemini_client.py  # API客户端
│   ├── summarizer.py     # 汇总处理
│   ├── storage.py        # 数据存储
│   └── utils.py          # 工具函数
├── data/
│   ├── processed.json    # 处理记录
│   ├── summaries/        # 汇总结果
│   └── config.ini        # 配置文件
├── logs/                 # 日志文件
├── tests/
│   ├── __init__.py
│   ├── test_config.py
│   ├── test_monitor.py
│   ├── test_client.py
│   ├── test_summarizer.py
│   └── test_storage.py
└── scripts/
    ├── install.sh        # 安装脚本
    └── run.sh           # 启动脚本
```

### 安装和配置

1. **环境准备**
   ```bash
   python -m venv venv
   source venv/bin/activate  # macOS/Linux
   pip install -r requirements.txt
   ```

2. **配置设置**
   ```bash
   cp .env.example .env
   # 编辑.env文件，设置GEMINI_API_KEY
   ```

3. **初始化运行**
   ```bash
   python main.py --init  # 初始化配置和目录
   python main.py --scan  # 执行初始扫描
   python main.py         # 启动监控模式
   ```

### 监控和日志

- **日志级别**: DEBUG, INFO, WARNING, ERROR
- **日志轮转**: 按日期和大小自动轮转
- **性能指标**: 处理速度、API调用次数、错误率
- **健康检查**: 定期检查API连接和文件系统状态