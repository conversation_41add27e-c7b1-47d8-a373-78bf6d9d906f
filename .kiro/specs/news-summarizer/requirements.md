# 需求文档

## 介绍

本项目旨在开发一个Python新闻汇总应用，该应用运行在本地桌面环境中，能够自动监控指定目录中的Markdown新闻文件，并使用Google Gemini 2.5 Flash API进行智能汇总。应用将从Obsidian同步的Cubox新闻内容进行处理，生成结构化的新闻摘要，帮助用户快速了解新闻要点。

## 需求

### 需求 1

**用户故事：** 作为用户，我希望应用能够自动监控指定目录中的新闻文件，这样我就不需要手动触发处理过程。

#### 验收标准

1. WHEN 应用启动 THEN 系统 SHALL 开始监控 `/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox` 目录
2. WHEN 目录中有新的.md文件添加 THEN 系统 SHALL 自动检测到文件变化
3. WHEN 现有.md文件被修改 THEN 系统 SHALL 识别文件更新并标记为待处理
4. IF 文件是隐藏文件或临时文件 THEN 系统 SHALL 忽略该文件
5. WHEN 系统检测到文件变化 THEN 系统 SHALL 记录变化日志

### 需求 2

**用户故事：** 作为用户，我希望应用能够使用Google Gemini API对新闻内容进行智能汇总，这样我就能快速获得新闻的核心信息。

#### 验收标准

1. WHEN 系统处理新闻文件 THEN 系统 SHALL 使用Google Gemini 2.5 Flash模型进行汇总
2. WHEN 文档超过4000字 THEN 系统 SHALL 采用分块汇总策略
3. WHEN API调用失败 THEN 系统 SHALL 实施重试机制，最多重试3次
4. IF API达到速率限制 THEN 系统 SHALL 等待并重新尝试请求
5. WHEN 汇总完成 THEN 系统 SHALL 生成包含核心事实、关键信息、重要程度和关键词的结构化输出

### 需求 3

**用户故事：** 作为用户，我希望汇总结果按照统一的格式输出，这样我就能快速理解和查找信息。

#### 验收标准

1. WHEN 生成汇总 THEN 系统 SHALL 包含标题、核心事实、关键信息、重要程度、关键词和汇总时间
2. WHEN 提取核心事实 THEN 系统 SHALL 包含5W1H要素（时间、地点、人物、事件、原因、影响）
3. WHEN 生成关键信息 THEN 系统 SHALL 提供3-5个要点
4. WHEN 评估重要程度 THEN 系统 SHALL 分为高、中、低三个等级并提供理由
5. WHEN 生成关键词 THEN 系统 SHALL 提供3-5个相关标签

### 需求 4

**用户故事：** 作为用户，我希望应用能够避免重复处理相同的文件，这样可以提高效率并节省API调用成本。

#### 验收标准

1. WHEN 系统处理文件 THEN 系统 SHALL 记录文件路径和最后修改时间到processed.json
2. WHEN 检查文件 THEN 系统 SHALL 验证文件是否已被处理过
3. IF 文件已处理且未修改 THEN 系统 SHALL 跳过该文件
4. WHEN 文件被修改 THEN 系统 SHALL 重新处理该文件
5. WHEN 系统启动 THEN 系统 SHALL 加载已处理文件列表

### 需求 5

**用户故事：** 作为用户，我希望应用提供灵活的配置选项，这样我就能根据需要调整应用行为。

#### 验收标准

1. WHEN 应用启动 THEN 系统 SHALL 从环境变量GEMINI_API_KEY读取API密钥
2. WHEN 配置扫描间隔 THEN 系统 SHALL 支持自定义时间间隔（默认30分钟）
3. WHEN 配置文件路径 THEN 系统 SHALL 支持通过配置文件自定义监控目录
4. WHEN 配置汇总参数 THEN 系统 SHALL 支持调整汇总长度和详细程度
5. WHEN 配置日志级别 THEN 系统 SHALL 支持INFO、DEBUG、ERROR三个级别

### 需求 6

**用户故事：** 作为用户，我希望应用能够妥善处理各种错误情况，这样应用就能稳定运行。

#### 验收标准

1. WHEN 网络连接失败 THEN 系统 SHALL 进入离线模式并创建重试队列
2. WHEN 文件访问被拒绝 THEN 系统 SHALL 记录错误并跳过该文件
3. WHEN Markdown格式无效 THEN 系统 SHALL 尝试容错处理或跳过文件
4. WHEN 内存不足 THEN 系统 SHALL 优化内存使用或分批处理
5. WHEN 发生未预期错误 THEN 系统 SHALL 记录详细错误日志并继续运行

### 需求 7

**用户故事：** 作为用户，我希望应用能够保存汇总结果并提供多种输出格式，这样我就能方便地查看和使用汇总信息。

#### 验收标准

1. WHEN 汇总完成 THEN 系统 SHALL 将结果保存为JSON格式到data/summaries目录
2. WHEN 保存汇总 THEN 系统 SHALL 包含原文路径、汇总内容、处理时间戳
3. WHEN 用户请求 THEN 系统 SHALL 支持生成Markdown格式的汇总报告
4. WHEN 显示进度 THEN 系统 SHALL 在控制台实时显示处理状态
5. WHEN 生成统计 THEN 系统 SHALL 提供处理文件数、成功率、API调用次数等信息

### 需求 8

**用户故事：** 作为用户，我希望应用支持批量处理和增量更新，这样我就能高效处理大量文件。

#### 验收标准

1. WHEN 应用首次启动 THEN 系统 SHALL 扫描并处理所有未汇总的文件
2. WHEN 进行增量更新 THEN 系统 SHALL 仅处理新增和修改的文件
3. WHEN 批量处理 THEN 系统 SHALL 限制同时处理的文件数量以控制资源使用
4. WHEN 处理大量文件 THEN 系统 SHALL 显示处理进度和预估完成时间
5. WHEN 用户中断处理 THEN 系统 SHALL 保存当前进度并支持从中断点继续

### 需求 9

**用户故事：** 作为用户，我希望应用提供命令行界面和手动控制选项，这样我就能灵活地使用应用。

#### 验收标准

1. WHEN 启动应用 THEN 系统 SHALL 支持命令行参数配置关键选项
2. WHEN 用户需要 THEN 系统 SHALL 支持手动触发汇总任务
3. WHEN 显示帮助 THEN 系统 SHALL 提供详细的使用说明
4. WHEN 查看状态 THEN 系统 SHALL 显示当前处理状态和统计信息
5. WHEN 用户退出 THEN 系统 SHALL 优雅地停止所有任务并保存状态